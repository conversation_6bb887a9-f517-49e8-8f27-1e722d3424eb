import { Component, Input, OnInit, QueryList, ViewChild, OnDestroy } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FlcColorSizeTableComponent } from 'fl-common-lib';
import { TranslateService } from '@ngx-translate/core';
import { OrderOutsourcingService } from '../order-outsourcing.service';
import { Line, Po } from '../models/order-outsourcing-interface';
import { NzMessageService } from 'ng-zorro-antd/message';
import { OrderStatus } from '../../garment-outsourcing/models/order-garment-outsourcing.enum';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-order-outsourcing-tabs-container',
  templateUrl: './order-outsourcing-tabs-container.component.html',
  styleUrls: ['./order-outsourcing-tabs-container.component.scss'],
})
export class OrderOutsourcingTabsContainerComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild('tabsRef') tabsRef!: any;
  @Input() tabsBodyTpl!: any;
  @Input() tabs!: Array<any>;
  @Input() modelType = false; // 是否是编辑模式。true:删除tab 添加tab,false:只读
  @Input() showPersonalInfo = false; // 是否显示个人信息 true 显示，false, 不显示
  @Input() colorSizeRefs!: QueryList<FlcColorSizeTableComponent>; // pos切换重置render
  @Input() colorSizeSurplusRefs!: QueryList<FlcColorSizeTableComponent>; // pos切换 合计部分重新render 目前仅发现加工厂内切换样式会有问题
  @Input() selectableList: any; // 用于加工厂选择交付单号
  @Input() parentGroup!: FormGroup;
  @Input() surplusPos!: any; // 剩余未分配的po
  @Input() factoryIndex!: number;
  @Input() getPoId!: any;
  @Input() orderStatus!: number;
  @Input() showLossCount = false;
  @Input() is_use_plan = false;
  selectedIndex = 0;
  selectedPlant = '';
  selectedPo = ''; // 添加交付单，用户选择的交付单号
  poCode: any = null; // 右上角选择的交付单 做滚动，点击tab需要将值制空
  orderStatusEnum = OrderStatus;
  changePoLineEmitter?: Subscription;
  factory_type = this.outsourcingService.factory_type;
  constructor(
    private outsourcingService: OrderOutsourcingService,
    private translateService: TranslateService,
    private _message: NzMessageService
  ) {}
  ngOnInit() {
    this.getPoId && this.getPoId(this.tabs[0]?.po_basic?.id);
    this.changePoLineEmitter = this.outsourcingService.changePoLineEmitter.subscribe(({ isPoChange, factoryIndex, poIndex, poLines }) => {
      if (isPoChange && factoryIndex === this.factoryIndex) {
        this.tabs.forEach((item: any, _index: number) => {
          if (poIndex === _index) {
            item.po_lines = poLines;
          }
        });
      }
    });
  }
  ngOnDestroy(): void {
    this.changePoLineEmitter?.unsubscribe();
  }
  ngOnChanges(): void {
    // 删除所有的po单 添加一个空的交付单 在点击添加剩余未分配 获取的poId为undefined 重新获取poId
    this.getPoId && this.getPoId(this.tabs[this.selectedIndex]?.po_basic?.id);
  }
  // 模糊查询，selected一个交付单。高亮并滚动
  onSelectChange(po_code: string) {
    const res = this.findCodeIndex(this.tabs, po_code);
    this.selectedIndex = res; // 会触发onSelectedIndexChange方法，方法中清空了this.poCode的值需要异步重新填充选中的值
    setTimeout(() => {
      this.poCode = po_code;
    }, 0);
  }
  // selected后查询当前的选中title 下拉切换选中状态
  findCodeIndex(list: any, po_code: string): number {
    return list.findIndex((tab: any) => {
      return tab?.po_basic?.po_code === po_code;
    });
  }
  // 关闭一个tab
  closeTab({ index }: { index: number }) {
    if (this.tabs[index].po_basic.deletable === false) {
      this._message.error(this.translateService.instant('outsourcingComponents.工厂已接单，无法删除'));
      return;
    }
    // 删除项不是空值
    if (this.tabs[index]?.po_basic?.id) {
      const title = `${this.translateService.instant('outsourcingComponents.确定')}<span class="red-mark">${this.translateService.instant(
        'btn.delete'
      )}</span>${this.translateService.instant('outsourcingComponents.当前交付单？')}`;
      this.outsourcingService.confirmDialog(title).afterClose.subscribe((val) => {
        if (val) {
          let result = this.tabs.splice(index, 1);
          this.getPoId && this.getPoId(this.tabs[this.selectedIndex]?.po_basic?.id);

          // 记录所有被删除的交付单行
          result.forEach((po: Po) => {
            po.po_lines.forEach((line: Line) => {
              // 如果有 order_id，说明这是已保存的数据，需要记录删除
              if (line.order_id) {
                this.outsourcingService.garmentEventEmitter.emit({
                  onchange: 'deleteDistributions',
                  result: line,
                });
              }
            });
          });

          // 去除空的交付单
          result = result.filter((po: Po) => {
            po.po_lines = po.po_lines.filter((line: Line) => {
              return line.qty || line._qty === 0;
            });
            return po.po_lines.length;
          });

          result.forEach((res) => {
            res.po_lines.forEach((line: any) => {
              const lines = this.parentGroup.value.lines.filter((item: any) => item.id != line.id && item.po_id !== line.po_id);
              this.parentGroup.get('lines')?.setValue(lines);
            });
          });
          this.parentGroup.get('pos')?.setValue(this.outsourcingService.deepclone(this.tabs));

          this.outsourcingService.resetSurplusPos({ value: result, type: 'merge', factoryIndex: this.factoryIndex });
          //二次工艺用
          this.outsourcingService.secProcessEventEmitter.emit({
            onchange: 'poChange',
            factoryIndex: this.factoryIndex,
            mode: 'removePo',
            result: result,
            showResult: this.tabs,
          });
          this.outsourcingService.garmentEventEmitter.emit({
            onchange: 'poChange',
            factoryIndex: this.factoryIndex,
            mode: 'removePo',
            result: this.tabs,
          });
          this.onSelectedIndexChange(this.selectedIndex);

          this.outsourcingService.garmentEventEmitter.emit({
            onchange: 'outsourcingQtyChange', // 成衣外发，外发数量变化,重新计算预付款金额
          });
        }
      });
    } else {
      this.tabs.splice(index, 1);
      this.getPoId && this.getPoId(this.tabs[this.selectedIndex]?.po_basic?.id);
      this.onSelectedIndexChange(this.selectedIndex);

      this.outsourcingService.garmentEventEmitter.emit({
        onchange: 'outsourcingQtyChange', // 成衣外发，外发数量变化,重新计算预付款金额
      });
    }
  }
  onSelectedIndexChange(idx: number) {
    // this.colorSizeRefs判断是否有值，删除全部的交付单后 只会变更为undefined 再次添加当前获取的到的也是undefined
    if (idx >= 0 && this.colorSizeRefs) {
      this.colorSizeRefs.get(idx)?.reRender();
      this.colorSizeSurplusRefs && this.colorSizeSurplusRefs.get(idx)?.reRender();
    }
    this.getPoId && this.getPoId(this.tabs[idx]?.po_basic?.id);
    this.poCode = null;
  }
  // 选择一个交付单
  onSelectedPo(code: any, index: number) {
    if (!code) {
      this.tabs[index] = [];
    } else {
      this.tabs[index] = this.outsourcingService.serialNumberLines(this.outsourcingService.getPoAllLine(this.findPoInfo(code)), false)[0];
      this.tabs = this.outsourcingService.deepclone(this.outsourcingService.serialPos(this.tabs));
      this.getPoId && this.getPoId(this.tabs[index]?.po_basic?.id);
      const lines: Array<Line> = this.outsourcingService.getLines(this.tabs);
      this.parentGroup.get('lines')?.reset(lines);
      this.parentGroup.get('pos')?.setValue(this.outsourcingService.deepclone(this.tabs));
      const result: any = this.surplusPos.filter((po: any) => {
        return po.po_basic.po_code !== code;
      });
      this.outsourcingService.resetSurplusPos({ value: result, type: 'reset', factoryIndex: this.factoryIndex });
      //二次工艺用
      this.outsourcingService.secProcessEventEmitter.emit({
        onchange: 'poChange',
        factoryIndex: this.factoryIndex,
        mode: 'addPo',
      });
      // 成衣加工用
      this.outsourcingService.garmentEventEmitter.emit({
        onchange: 'poChange',
        factoryIndex: this.factoryIndex,
        mode: 'addPo',
      });
      this.outsourcingService.garmentEventEmitter.emit({
        onchange: 'outsourcingQtyChange', // 成衣外发，外发数量变化,重新计算预付款金额
      });
    }
  }
  lossCountChange(event: any, index: number, po_unique_code: string) {
    const losscount = (event.target as HTMLInputElement).value;
    this.outsourcingService.garmentEventEmitter.emit({
      onchange: 'lossCountChange',
      factoryIndex: this.factoryIndex,
      poUniqueCode: po_unique_code,
      losscount: losscount,
    });
  }

  dueTimeChange(event: any, index: number, po_unique_code: string) {
    // 处理日期清空操作，确保传递 null 而不是 Invalid Date
    const dueTime = event ? event : null;

    this.outsourcingService.garmentEventEmitter.emit({
      onchange: 'dueTimeChange',
      factoryIndex: this.factoryIndex,
      poUniqueCode: po_unique_code,
      due_time: dueTime,
    });
  }
  // 通过po_code查询当前po信息
  findPoInfo(code: string) {
    return this.selectableList.filter((tab: any) => {
      return tab?.po_basic?.po_code === code;
    });
  }
}
